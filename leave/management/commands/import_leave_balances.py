"""
Django management command to import leave balances from CSV file as per HRMS-202 requirements.

This command processes a CSV file containing employee leave balances and updates the
AvailableLeave records in the database.
"""

import csv
import os
from decimal import Decimal, InvalidOperation
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from employee.models import Employee
from leave.models import LeaveType, AvailableLeave


class Command(BaseCommand):
    help = 'Import leave balances from CSV file'

    def add_arguments(self, parser):
        parser.add_argument(
            'csv_file',
            type=str,
            help='Path to the CSV file containing leave balances'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Validate CSV without making database changes'
        )
        parser.add_argument(
            '--skip-errors',
            action='store_true',
            help='Continue processing even if some records have errors'
        )
        parser.add_argument(
            '--update-existing',
            action='store_true',
            help='Update existing leave balance records'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        csv_file = options['csv_file']
        print(csv_file)
        print(os.getcwd())
        
        # Validate file exists
        if not os.path.exists(csv_file):
            # os.makedirs(csv_file)
            # os.touch(csv_file)
            raise CommandError(f'CSV file not found: {csv_file}')

        self.stdout.write(
            self.style.SUCCESS(f'Processing CSV file: {csv_file}')
        )

        # Read and validate CSV
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                csv_data = list(csv.DictReader(file))
        except Exception as e:
            raise CommandError(f'Error reading CSV file: {str(e)}')

        if not csv_data:
            raise CommandError('CSV file is empty or has no data rows')

        # Validate CSV structure
        self._validate_csv_structure(csv_data[0])

        # Process the data
        if options['dry_run']:
            self._dry_run_validation(csv_data, options)
        else:
            self._import_leave_balances(csv_data, options)

    def _validate_csv_structure(self, first_row):
        """Validate that CSV has required columns"""
        required_columns = ['Employee ID']
        
        # Check for required columns
        missing_columns = []
        for col in required_columns:
            if col not in first_row:
                missing_columns.append(col)
        
        if missing_columns:
            raise CommandError(
                f'Missing required columns in CSV: {", ".join(missing_columns)}'
            )

        # Identify leave type columns
        leave_columns = []
        for col in first_row.keys():
            if col.endswith(' (Balance)') or col.endswith(' (Total)'):
                leave_columns.append(col)
        
        if not leave_columns:
            raise CommandError(
                'No leave balance columns found in CSV. '
                'Expected columns like "Earned Leave Balance", "Sick Leave Total", etc.'
            )

        self.stdout.write(
            self.style.SUCCESS(f'CSV structure validated. Found {len(leave_columns)} leave columns.')
        )

    def _dry_run_validation(self, csv_data, options):
        """Perform dry run validation without making changes"""
        self.stdout.write(
            self.style.WARNING('--- DRY RUN MODE ---')
        )
        
        valid_records = 0
        error_records = 0
        errors = []

        for row_num, row in enumerate(csv_data, start=2):  # Start at 2 for header
            row_errors = self._validate_row(row, row_num)
            
            if row_errors:
                error_records += 1
                errors.extend(row_errors)
            else:
                valid_records += 1

        # Report results
        self.stdout.write(f'\nValidation Results:')
        self.stdout.write(f'  Valid records: {valid_records}')
        self.stdout.write(f'  Error records: {error_records}')
        self.stdout.write(f'  Total records: {len(csv_data)}')

        if errors:
            self.stdout.write(
                self.style.ERROR(f'\nFound {len(errors)} errors:')
            )
            for error in errors[:10]:  # Show first 10 errors
                self.stdout.write(f'  - {error}')
            
            if len(errors) > 10:
                self.stdout.write(f'  ... and {len(errors) - 10} more errors')

    def _import_leave_balances(self, csv_data, options):
        """Import leave balances into the database"""
        self.stdout.write('Starting leave balance import...')
        
        successful_imports = 0
        failed_imports = 0
        errors = []

        with transaction.atomic():
            for row_num, row in enumerate(csv_data, start=2):
                try:
                    print(row)
                    row_errors = self._validate_row(row, row_num)
                    
                    if row_errors:
                        failed_imports += 1
                        errors.extend(row_errors)
                        
                        if not options['skip_errors']:
                            raise CommandError(
                                f'Validation errors found. Use --skip-errors to continue. '
                                f'First error: {row_errors[0]}'
                            )
                        continue

                    # Process the row
                    self._process_row(row, options)
                    successful_imports += 1

                except Exception as e:
                    failed_imports += 1
                    error_msg = f'Row {row_num}: {str(e)}'
                    errors.append(error_msg)
                    
                    if not options['skip_errors']:
                        raise CommandError(error_msg)

        # Report results
        self.stdout.write(
            self.style.SUCCESS(f'\nImport completed:')
        )
        self.stdout.write(f'  Successful imports: {successful_imports}')
        self.stdout.write(f'  Failed imports: {failed_imports}')
        
        if errors:
            self.stdout.write(
                self.style.WARNING(f'  Errors encountered: {len(errors)}')
            )

    def _validate_row(self, row, row_num):
        """Validate a single CSV row"""
        errors = []
        employee_id = row.get('Employee ID', '').strip()
        
        # Validate employee exists
        if not employee_id:
            errors.append(f'Row {row_num}: Missing Employee ID')
        else:
            try:
                Employee.objects.get(badge_id=employee_id)
            except Employee.DoesNotExist:
                errors.append(f'Row {row_num}: Employee not found: {employee_id}')

        # Validate leave balance data
        for col, value in row.items():
            if col.endswith(' (Balance)') or col.endswith(' (Total)'):
                if value.strip():  # Only validate non-empty values
                    try:
                        decimal_value = Decimal(value)
                        if decimal_value < 0:
                            errors.append(f'Row {row_num}: Negative value not allowed: {col} = {value}')
                    except (InvalidOperation, ValueError):
                        errors.append(f'Row {row_num}: Invalid numeric value: {col} = {value}')

        return errors

    def _process_row(self, row, options):
        """Process a single CSV row and update database"""
        employee_id = row['Employee ID'].strip()
        employee = Employee.objects.get(badge_id=employee_id)

        # Process leave balance columns
        leave_data = {}
        for col, value in row.items():
            print('-------------')
            print(col, value)
            if col.endswith(' (Balance)') or col.endswith(' (Total)'):
                if value.strip():  # Only process non-empty values
                    leave_type_name = col.replace(' (Balance)', '').replace(' (Total)', '')
                    print(leave_data)
                    print(leave_type_name)
                    
                    if leave_type_name not in leave_data:
                        leave_data[leave_type_name] = {}
                    
                    if col.endswith(' (Balance)'):
                        leave_data[leave_type_name]['balance'] = Decimal(value)
                    else:  # Total
                        leave_data[leave_type_name]['total'] = Decimal(value)

        # Create or update AvailableLeave records
        for leave_type_name, data in leave_data.items():
            try:
                leave_type = LeaveType.objects.get(name=leave_type_name)
                
                available_leave, created = AvailableLeave.objects.get_or_create(
                    employee_id=employee,
                    leave_type_id=leave_type,
                    defaults={
                        'available_days': float(data['balance']) if data['balance'] else 0.0,
                        'total_leave_days': float(data.get('total', data['balance'])) if data.get('total', data['balance']) else 0.0,
                        'carryforward_days': 0.0
                    }
                )
                
                if not created and options['update_existing']:
                    available_leave.available_days = float(data['balance'])
                    available_leave.total_leave_days = float(data.get('total', data['balance']))
                    available_leave.save()

            except LeaveType.DoesNotExist:
                # Skip unknown leave types
                continue
