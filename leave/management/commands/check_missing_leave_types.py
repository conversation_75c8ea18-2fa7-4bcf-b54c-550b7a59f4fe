"""
Django management command to check for missing leave types as per HRMS-202 requirements.

This command validates that all required leave types exist in the database and can
optionally create missing leave types with appropriate configurations.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from leave.models import LeaveType
from base.models import Company


class Command(BaseCommand):
    help = 'Check for missing leave types and optionally create them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-missing',
            action='store_true',
            help='Create missing leave types automatically',
        )
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create leave types for (required if --create-missing is used)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating leave types',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        self.stdout.write(
            self.style.SUCCESS('Checking for missing leave types...')
        )

        # Define required leave types with their configurations
        required_leave_types = {
            "Earned Leave": {
                "payment": "paid",
                "total_days": 21.0,
                "carryforward_type": "carryforward",
                "carryforward_max": 5.0,
                "reset": True,
                "reset_based": "yearly",
                "reset_month": "1",
                "reset_day": "1"
            },
            "Sick Leave": {
                "payment": "paid",
                "total_days": 12.0,
                "require_attachment": "yes",
                "exclude_holiday": "no",
                "exclude_company_leave": "no"
            },
            "Casual Leave": {
                "payment": "paid",
                "total_days": 12.0,
                "require_attachment": "no",
                "exclude_holiday": "yes",
                "exclude_company_leave": "yes"
            },
            "Maternity Leave": {
                "payment": "paid",
                "total_days": 180.0,
                "require_attachment": "yes",
                "require_approval": "yes"
            },
            "Paternity Leave": {
                "payment": "paid",
                "total_days": 15.0,
                "require_attachment": "yes",
                "require_approval": "yes"
            },
            "Compensatory Leave": {
                "payment": "paid",
                "total_days": 0.0,
                "is_compensatory_leave": True,
                "require_approval": "yes"
            }
        }

        # Get existing leave types
        existing_types = set(LeaveType.objects.values_list('name', flat=True))
        
        # Find missing leave types
        missing_types = []
        for required_type in required_leave_types.keys():
            if required_type not in existing_types:
                missing_types.append(required_type)

        if not missing_types:
            self.stdout.write(
                self.style.SUCCESS('✓ All required leave types are present in the database.')
            )
            return

        # Report missing leave types
        self.stdout.write(
            self.style.WARNING(f'Found {len(missing_types)} missing leave types:')
        )
        for leave_type in missing_types:
            self.stdout.write(f'  - {leave_type}')

        # Handle creation of missing leave types
        if options['create_missing']:
            if not options['company_id']:
                raise CommandError(
                    'Company ID is required when using --create-missing. '
                    'Use --company-id=<id> to specify the company.'
                )

            try:
                company = Company.objects.get(id=options['company_id'])
            except Company.DoesNotExist:
                raise CommandError(f'Company with ID {options["company_id"]} does not exist.')

            if options['dry_run']:
                self.stdout.write(
                    self.style.WARNING('\n--- DRY RUN MODE ---')
                )
                self.stdout.write('The following leave types would be created:')
                for leave_type in missing_types:
                    config = required_leave_types[leave_type]
                    self.stdout.write(f'  - {leave_type}: {config}')
                return

            # Create missing leave types
            self._create_missing_leave_types(missing_types, required_leave_types, company)
        else:
            self.stdout.write(
                self.style.WARNING(
                    '\nTo create missing leave types, run with --create-missing --company-id=<id>'
                )
            )

    def _create_missing_leave_types(self, missing_types, required_leave_types, company):
        """Create missing leave types in the database"""
        self.stdout.write(
            self.style.SUCCESS(f'\nCreating {len(missing_types)} missing leave types...')
        )

        created_count = 0
        
        with transaction.atomic():
            for leave_type_name in missing_types:
                try:
                    config = required_leave_types[leave_type_name]
                    
                    # Create the leave type
                    leave_type = LeaveType.objects.create(
                        name=leave_type_name,
                        company_id=company,
                        **config
                    )
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✓ Created: {leave_type_name}')
                    )
                    created_count += 1
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ Failed to create {leave_type_name}: {str(e)}')
                    )

        self.stdout.write(
            self.style.SUCCESS(f'\nSuccessfully created {created_count} leave types.')
        )

        # Verify creation
        self._verify_leave_types_created(missing_types)

    def _verify_leave_types_created(self, expected_types):
        """Verify that the leave types were created successfully"""
        self.stdout.write('\nVerifying created leave types...')
        
        for leave_type_name in expected_types:
            try:
                leave_type = LeaveType.objects.get(name=leave_type_name)
                self.stdout.write(
                    self.style.SUCCESS(f'  ✓ Verified: {leave_type_name} (ID: {leave_type.id})')
                )
            except LeaveType.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ Missing: {leave_type_name}')
                )
