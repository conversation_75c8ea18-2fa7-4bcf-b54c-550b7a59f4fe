import os
import tempfile
from datetime import date, datetime
from decimal import Decimal
from io import StringIO
from unittest.mock import patch, MagicMock

import pandas as pd
from django.core.management import call_command
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from employee.models import Employee, EmployeeWorkInformation
from base.models import Company, Department, JobPosition
from leave.models import LeaveType, AvailableLeave
# Management commands will be tested via call_command


class LeaveTypesValidationTestCase(TestCase):
    """Test cases for validating leave types as per HRMS-202 requirements"""

    def setUp(self):
        """Set up test data"""
        self.company = Company.objects.create(company="Test Company")
        self.department = Department.objects.create(department="IT")
        self.department.company_id.set([self.company])
        self.job_position = JobPosition.objects.create(job_position="Developer", department_id=self.department)
        self.job_position.company_id.set([self.company])

        # Create test user and employee
        self.user = User.objects.create_user(username="testuser", email="<EMAIL>")
        self.employee = Employee.objects.create(
            badge_id="EMP001",
            employee_first_name="John",
            employee_last_name="Doe",
            email="<EMAIL>",
            phone="*********0",
            employee_user_id=self.user
        )

        # Create employee work information
        self.work_info = EmployeeWorkInformation.objects.create(
            employee_id=self.employee,
            job_position_id=self.job_position,
            department_id=self.department,
            company_id=self.company,
            date_joining=date.today()
        )

    def test_create_earned_leave_type(self):
        """Test creation of Earned Leave type"""
        earned_leave = LeaveType.objects.create(
            name="Earned Leave",
            payment="paid",
            total_days=21.0,
            company_id=self.company
        )
        self.assertEqual(earned_leave.name, "Earned Leave")
        self.assertEqual(earned_leave.payment, "paid")
        self.assertEqual(earned_leave.total_days, 21.0)
        self.assertFalse(earned_leave.is_compensatory_leave)

    def test_create_sick_leave_type(self):
        """Test creation of Sick Leave type"""
        sick_leave = LeaveType.objects.create(
            name="Sick Leave",
            payment="paid",
            total_days=12.0,
            company_id=self.company,
            require_attachment="yes"
        )
        self.assertEqual(sick_leave.name, "Sick Leave")
        self.assertEqual(sick_leave.payment, "paid")
        self.assertEqual(sick_leave.total_days, 12.0)
        self.assertEqual(sick_leave.require_attachment, "yes")

    def test_leave_type_unique_compensatory_constraint(self):
        """Test that only one compensatory leave type can exist"""
        # Create first compensatory leave
        comp_leave1 = LeaveType.objects.create(
            name="Compensatory Leave",
            payment="paid",
            total_days=0.0,
            is_compensatory_leave=True,
            company_id=self.company
        )

        # Try to create second compensatory leave - should raise validation error
        comp_leave2 = LeaveType(
            name="Another Comp Leave",
            payment="paid",
            total_days=0.0,
            is_compensatory_leave=True,
            company_id=self.company
        )

        with self.assertRaises(ValidationError):
            comp_leave2.clean()

    def test_leave_type_carryforward_settings(self):
        """Test leave type carryforward configurations"""
        leave_type = LeaveType.objects.create(
            name="Annual Leave",
            payment="paid",
            total_days=25.0,
            carryforward_type="carryforward",
            carryforward_max=5.0,
            company_id=self.company
        )

        self.assertEqual(leave_type.carryforward_type, "carryforward")
        self.assertEqual(leave_type.carryforward_max, 5.0)

    def test_leave_type_reset_configurations(self):
        """Test leave type reset configurations"""
        leave_type = LeaveType.objects.create(
            name="Monthly Leave",
            payment="paid",
            total_days=2.0,
            reset=True,
            reset_based="monthly",
            reset_day="1",
            company_id=self.company
        )

        self.assertTrue(leave_type.reset)
        self.assertEqual(leave_type.reset_based, "monthly")
        self.assertEqual(leave_type.reset_day, "1")


class EmployeeBadgeIdMappingTestCase(TestCase):
    """Test cases for employee badge ID mapping functionality"""

    def setUp(self):
        """Set up test data"""
        self.company = Company.objects.create(company="Test Company")
        self.department = Department.objects.create(department="HR")
        self.department.company_id.set([self.company])
        self.job_position = JobPosition.objects.create(job_position="Manager", department_id=self.department)
        self.job_position.company_id.set([self.company])

        # Create multiple test employees with different badge IDs
        self.employees = []
        for i in range(5):
            user = User.objects.create_user(
                username=f"user{i}",
                email=f"user{i}@example.com"
            )
            employee = Employee.objects.create(
                badge_id=f"RZP{i:03d}",  # Format: RZP001, RZP002, etc.
                employee_first_name=f"Employee{i}",
                employee_last_name="Test",
                email=f"employee{i}@example.com",
                phone=f"*********{i}",
                employee_user_id=user
            )

            EmployeeWorkInformation.objects.create(
                employee_id=employee,
                job_position_id=self.job_position,
                department_id=self.department,
                company_id=self.company,
                date_joining=date.today()
            )

            self.employees.append(employee)

    def test_employee_badge_id_uniqueness(self):
        """Test that badge IDs are unique"""
        # Try to create employee with duplicate badge ID
        user = User.objects.create_user(username="duplicate", email="<EMAIL>")

        with self.assertRaises(IntegrityError):
            Employee.objects.create(
                badge_id="RZP001",  # Duplicate badge ID
                employee_first_name="Duplicate",
                employee_last_name="Employee",
                email="<EMAIL>",
                phone="9999999999",
                employee_user_id=user
            )

    def test_find_employee_by_badge_id(self):
        """Test finding employees by badge ID"""
        employee = Employee.objects.get(badge_id="RZP002")
        self.assertEqual(employee.employee_first_name, "Employee2")
        self.assertEqual(employee.badge_id, "RZP002")

    def test_badge_id_case_insensitive_lookup(self):
        """Test case-insensitive badge ID lookup"""
        # Test lowercase lookup
        employee = Employee.objects.filter(badge_id__iexact="rzp001").first()
        self.assertIsNotNone(employee)
        self.assertEqual(employee.badge_id, "RZP001")

    def test_missing_badge_id_handling(self):
        """Test handling of employees without badge IDs"""
        user = User.objects.create_user(username="nobadge", email="<EMAIL>")
        employee = Employee.objects.create(
            badge_id=None,  # No badge ID
            employee_first_name="No",
            employee_last_name="Badge",
            email="<EMAIL>",
            phone="0000000000",
            employee_user_id=user
        )

        # Should be able to create employee without badge ID
        self.assertIsNone(employee.badge_id)

        # Should not be included in badge ID queries
        employees_with_badges = Employee.objects.exclude(badge_id__isnull=True).exclude(badge_id__exact="")
        self.assertNotIn(employee, employees_with_badges)


class LeaveBalanceMigrationTestCase(TestCase):
    """Test cases for leave balance migration functionality"""

    def setUp(self):
        """Set up test data"""
        self.company = Company.objects.create(company="Razorpay")
        self.department = Department.objects.create(department="Engineering")
        self.department.company_id.set([self.company])
        self.job_position = JobPosition.objects.create(job_position="Software Engineer", department_id=self.department)
        self.job_position.company_id.set([self.company])

        # Create leave types that might be in CSV
        self.earned_leave = LeaveType.objects.create(
            name="Earned Leave",
            payment="paid",
            total_days=21.0,
            company_id=self.company
        )

        self.sick_leave = LeaveType.objects.create(
            name="Sick Leave",
            payment="paid",
            total_days=12.0,
            company_id=self.company
        )

        self.casual_leave = LeaveType.objects.create(
            name="Casual Leave",
            payment="paid",
            total_days=12.0,
            company_id=self.company
        )

        # Create test employees
        self.employees = []
        for i in range(3):
            user = User.objects.create_user(
                username=f"emp{i}",
                email=f"emp{i}@razorpay.com"
            )
            employee = Employee.objects.create(
                badge_id=f"RZP{i+100:03d}",  # RZP100, RZP101, RZP102
                employee_first_name=f"Employee{i}",
                employee_last_name="Razorpay",
                email=f"employee{i}@razorpay.com",
                phone=f"*********{i}",
                employee_user_id=user
            )

            EmployeeWorkInformation.objects.create(
                employee_id=employee,
                job_position_id=self.job_position,
                department_id=self.department,
                company_id=self.company,
                date_joining=date.today()
            )

            self.employees.append(employee)

    def test_create_available_leave_for_employee(self):
        """Test creating available leave records for employees"""
        employee = self.employees[0]

        # Create available leave for earned leave
        available_leave = AvailableLeave.objects.create(
            employee_id=employee,
            leave_type_id=self.earned_leave,
            available_days=21.0,
            carryforward_days=0.0,
            total_leave_days=21.0
        )

        self.assertEqual(available_leave.employee_id, employee)
        self.assertEqual(available_leave.leave_type_id, self.earned_leave)
        self.assertEqual(available_leave.available_days, 21.0)
        self.assertEqual(available_leave.total_leave_days, 21.0)

    def test_update_existing_leave_balance(self):
        """Test updating existing leave balance"""
        employee = self.employees[0]

        # Create initial available leave
        available_leave = AvailableLeave.objects.create(
            employee_id=employee,
            leave_type_id=self.earned_leave,
            available_days=21.0,
            carryforward_days=0.0,
            total_leave_days=21.0
        )

        # Update the balance
        available_leave.available_days = 18.5
        available_leave.carryforward_days = 2.5
        available_leave.total_leave_days = 21.0
        available_leave.save()

        # Verify update
        updated_leave = AvailableLeave.objects.get(id=available_leave.id)
        self.assertEqual(updated_leave.available_days, 18.5)
        self.assertEqual(updated_leave.carryforward_days, 2.5)
        self.assertEqual(updated_leave.total_leave_days, 21.0)

    def test_unique_constraint_employee_leave_type(self):
        """Test unique constraint on employee and leave type combination"""
        employee = self.employees[0]

        # Create first available leave
        AvailableLeave.objects.create(
            employee_id=employee,
            leave_type_id=self.earned_leave,
            available_days=21.0,
            carryforward_days=0.0,
            total_leave_days=21.0
        )

        # Try to create duplicate - should raise IntegrityError
        with self.assertRaises(IntegrityError):
            AvailableLeave.objects.create(
                employee_id=employee,
                leave_type_id=self.earned_leave,
                available_days=15.0,
                carryforward_days=0.0,
                total_leave_days=15.0
            )

    def test_bulk_create_leave_balances(self):
        """Test bulk creation of leave balances for multiple employees"""
        leave_balances = []

        for employee in self.employees:
            # Create earned leave balance
            leave_balances.append(AvailableLeave(
                employee_id=employee,
                leave_type_id=self.earned_leave,
                available_days=21.0,
                carryforward_days=0.0,
                total_leave_days=21.0
            ))

            # Create sick leave balance
            leave_balances.append(AvailableLeave(
                employee_id=employee,
                leave_type_id=self.sick_leave,
                available_days=12.0,
                carryforward_days=0.0,
                total_leave_days=12.0
            ))

        # Bulk create
        AvailableLeave.objects.bulk_create(leave_balances)

        # Verify creation
        total_created = AvailableLeave.objects.count()
        self.assertEqual(total_created, 6)  # 3 employees × 2 leave types

        # Verify specific records
        for employee in self.employees:
            earned_balance = AvailableLeave.objects.get(
                employee_id=employee,
                leave_type_id=self.earned_leave
            )
            self.assertEqual(earned_balance.available_days, 21.0)

            sick_balance = AvailableLeave.objects.get(
                employee_id=employee,
                leave_type_id=self.sick_leave
            )
            self.assertEqual(sick_balance.available_days, 12.0)


class CSVImportFunctionalityTestCase(TestCase):
    """Test cases for CSV import functionality"""

    def setUp(self):
        """Set up test data"""
        self.company = Company.objects.create(company="Razorpay")
        self.department = Department.objects.create(department="Engineering")
        self.department.company_id.set([self.company])
        self.job_position = JobPosition.objects.create(job_position="Developer", department_id=self.department)

        # Create leave types
        self.leave_types = {
            "Earned Leave": LeaveType.objects.create(
                name="Earned Leave",
                payment="paid",
                total_days=21.0,
                company_id=self.company
            ),
            "Sick Leave": LeaveType.objects.create(
                name="Sick Leave",
                payment="paid",
                total_days=12.0,
                company_id=self.company
            ),
            "Casual Leave": LeaveType.objects.create(
                name="Casual Leave",
                payment="paid",
                total_days=12.0,
                company_id=self.company
            )
        }

        # Create test employees
        self.employees = []
        badge_ids = ["RZP001", "RZP002", "RZP003"]

        for i, badge_id in enumerate(badge_ids):
            user = User.objects.create_user(
                username=f"user{badge_id}",
                email=f"user{badge_id.lower()}@razorpay.com"
            )
            employee = Employee.objects.create(
                badge_id=badge_id,
                employee_first_name=f"Employee{i+1}",
                employee_last_name="Test",
                email=f"employee{i+1}@razorpay.com",
                phone=f"*********{i}",
                employee_user_id=user
            )

            EmployeeWorkInformation.objects.create(
                employee_id=employee,
                job_position_id=self.job_position,
                department_id=self.department,
                company_id=self.company,
                date_joining=date.today()
            )

            self.employees.append(employee)

    def create_test_csv_content(self):
        """Create test CSV content for leave balances"""
        csv_content = """Employee ID,Earned Leave Balance,Earned Leave Total,Sick Leave Balance,Sick Leave Total,Casual Leave Balance,Casual Leave Total
RZP001,18.5,21.0,10.0,12.0,8.5,12.0
RZP002,20.0,21.0,12.0,12.0,11.0,12.0
RZP003,15.5,21.0,8.0,12.0,9.5,12.0"""
        return csv_content

    def test_parse_csv_content(self):
        """Test parsing CSV content"""
        csv_content = self.create_test_csv_content()

        # Parse CSV using pandas
        from io import StringIO
        df = pd.read_csv(StringIO(csv_content))

        # Verify structure
        self.assertEqual(len(df), 3)  # 3 employees
        self.assertIn("Employee ID", df.columns)
        self.assertIn("Earned Leave Balance", df.columns)
        self.assertIn("Sick Leave Balance", df.columns)

        # Verify data
        first_row = df.iloc[0]
        self.assertEqual(first_row["Employee ID"], "RZP001")
        self.assertEqual(first_row["Earned Leave Balance"], 18.5)
        self.assertEqual(first_row["Sick Leave Balance"], 10.0)

    def test_validate_csv_employee_ids(self):
        """Test validation of employee IDs in CSV"""
        csv_content = self.create_test_csv_content()
        df = pd.read_csv(StringIO(csv_content))

        # Get all badge IDs from database
        db_badge_ids = set(Employee.objects.values_list('badge_id', flat=True))
        csv_badge_ids = set(df['Employee ID'].tolist())

        # Check which CSV badge IDs exist in database
        valid_badge_ids = csv_badge_ids.intersection(db_badge_ids)
        invalid_badge_ids = csv_badge_ids - db_badge_ids

        self.assertEqual(len(valid_badge_ids), 3)  # All should be valid
        self.assertEqual(len(invalid_badge_ids), 0)  # None should be invalid

    def test_csv_with_invalid_employee_ids(self):
        """Test CSV processing with invalid employee IDs"""
        csv_content = """Employee ID,Earned Leave Balance,Earned Leave Total
RZP001,18.5,21.0
INVALID001,20.0,21.0
RZP002,15.5,21.0"""

        df = pd.read_csv(StringIO(csv_content))

        # Get valid and invalid badge IDs
        db_badge_ids = set(Employee.objects.values_list('badge_id', flat=True))
        csv_badge_ids = set(df['Employee ID'].tolist())

        valid_badge_ids = csv_badge_ids.intersection(db_badge_ids)
        invalid_badge_ids = csv_badge_ids - db_badge_ids

        self.assertEqual(len(valid_badge_ids), 2)  # RZP001, RZP002
        self.assertEqual(len(invalid_badge_ids), 1)  # INVALID001
        self.assertIn("INVALID001", invalid_badge_ids)

    def test_process_csv_leave_data(self):
        """Test processing CSV data to update leave balances"""
        csv_content = self.create_test_csv_content()
        df = pd.read_csv(StringIO(csv_content))

        # Process each row
        for _, row in df.iterrows():
            employee_id = row['Employee ID']
            try:
                employee = Employee.objects.get(badge_id=employee_id)

                # Process Earned Leave
                earned_balance = row['Earned Leave Balance']
                earned_total = row['Earned Leave Total']

                available_leave, created = AvailableLeave.objects.get_or_create(
                    employee_id=employee,
                    leave_type_id=self.leave_types['Earned Leave'],
                    defaults={
                        'available_days': earned_balance,
                        'total_leave_days': earned_total,
                        'carryforward_days': 0.0
                    }
                )

                if not created:
                    available_leave.available_days = earned_balance
                    available_leave.total_leave_days = earned_total
                    available_leave.save()

            except Employee.DoesNotExist:
                continue  # Skip invalid employee IDs

        # Verify the data was processed correctly
        emp1 = Employee.objects.get(badge_id="RZP001")
        earned_leave_emp1 = AvailableLeave.objects.get(
            employee_id=emp1,
            leave_type_id=self.leave_types['Earned Leave']
        )
        self.assertEqual(earned_leave_emp1.available_days, 18.5)
        self.assertEqual(earned_leave_emp1.total_leave_days, 21.0)


class DjangoManagementCommandTestCase(TransactionTestCase):
    """Test cases for Django management commands related to leave migration"""

    def setUp(self):
        """Set up test data"""
        self.company = Company.objects.create(company="Test Company")
        self.department = Department.objects.create(department="IT")
        self.department.company_id.set([self.company])

        # Create some leave types
        self.existing_leave_types = [
            "Annual Leave",
            "Casual Leave"
        ]

        for leave_type_name in self.existing_leave_types:
            LeaveType.objects.create(
                name=leave_type_name,
                payment="paid",
                total_days=12.0,
                company_id=self.company
            )

    def test_check_missing_leave_types_command_structure(self):
        """Test the structure and basic functionality of check missing leave types command"""
        # Define required leave types that should exist
        required_leave_types = [
            "Earned Leave",
            "Sick Leave",
            "Casual Leave",
            "Annual Leave",
            "Maternity Leave",
            "Paternity Leave"
        ]

        # Get existing leave types
        existing_types = set(LeaveType.objects.values_list('name', flat=True))

        # Find missing leave types
        missing_types = []
        for required_type in required_leave_types:
            if required_type not in existing_types:
                missing_types.append(required_type)

        # Should find missing leave types
        expected_missing = ["Earned Leave", "Sick Leave", "Maternity Leave", "Paternity Leave"]
        self.assertEqual(set(missing_types), set(expected_missing))

    def test_create_missing_leave_types(self):
        """Test creating missing leave types"""
        # Define leave types that should exist with their configurations
        required_leave_types = {
            "Earned Leave": {
                "payment": "paid",
                "total_days": 21.0,
                "carryforward_type": "carryforward",
                "carryforward_max": 5.0
            },
            "Sick Leave": {
                "payment": "paid",
                "total_days": 12.0,
                "require_attachment": "yes"
            },
            "Maternity Leave": {
                "payment": "paid",
                "total_days": 180.0,
                "require_attachment": "yes"
            }
        }

        # Create missing leave types
        created_types = []
        for leave_type_name, config in required_leave_types.items():
            if not LeaveType.objects.filter(name=leave_type_name).exists():
                leave_type = LeaveType.objects.create(
                    name=leave_type_name,
                    company_id=self.company,
                    **config
                )
                created_types.append(leave_type)

        # Verify creation
        self.assertEqual(len(created_types), 3)

        # Verify specific configurations
        earned_leave = LeaveType.objects.get(name="Earned Leave")
        self.assertEqual(earned_leave.total_days, 21.0)
        self.assertEqual(earned_leave.carryforward_type, "carryforward")

        sick_leave = LeaveType.objects.get(name="Sick Leave")
        self.assertEqual(sick_leave.require_attachment, "yes")
        self.assertEqual(sick_leave.total_days, 12.0)

    def test_bulk_import_validation(self):
        """Test validation logic for bulk import"""
        # Create test employees
        employees = []
        for i in range(3):
            user = User.objects.create_user(
                username=f"bulkuser{i}",
                email=f"bulk{i}@test.com"
            )
            employee = Employee.objects.create(
                badge_id=f"BULK{i:03d}",
                employee_first_name=f"Bulk{i}",
                employee_last_name="Test",
                email=f"bulk{i}@test.com",
                phone=f"555000{i:03d}",
                employee_user_id=user
            )
            employees.append(employee)

        # Create leave types
        earned_leave = LeaveType.objects.create(
            name="Earned Leave",
            payment="paid",
            total_days=21.0,
            company_id=self.company
        )

        # Test data validation
        test_data = [
            {"badge_id": "BULK000", "leave_type": "Earned Leave", "balance": 18.5, "total": 21.0},
            {"badge_id": "BULK001", "leave_type": "Earned Leave", "balance": 20.0, "total": 21.0},
            {"badge_id": "INVALID", "leave_type": "Earned Leave", "balance": 15.0, "total": 21.0},  # Invalid employee
            {"badge_id": "BULK002", "leave_type": "Invalid Leave", "balance": 10.0, "total": 12.0},  # Invalid leave type
        ]

        valid_records = []
        error_records = []

        for record in test_data:
            errors = []

            # Validate employee
            try:
                employee = Employee.objects.get(badge_id=record["badge_id"])
            except Employee.DoesNotExist:
                errors.append(f"Employee with badge ID {record['badge_id']} not found")
                employee = None

            # Validate leave type
            try:
                leave_type = LeaveType.objects.get(name=record["leave_type"])
            except LeaveType.DoesNotExist:
                errors.append(f"Leave type {record['leave_type']} not found")
                leave_type = None

            # Validate numeric values
            if record["balance"] < 0:
                errors.append("Balance cannot be negative")

            if record["total"] < record["balance"]:
                errors.append("Total days cannot be less than balance")

            if errors:
                error_records.append({**record, "errors": errors})
            else:
                valid_records.append({
                    "employee": employee,
                    "leave_type": leave_type,
                    "balance": record["balance"],
                    "total": record["total"]
                })

        # Verify validation results
        self.assertEqual(len(valid_records), 2)  # BULK000 and BULK001
        self.assertEqual(len(error_records), 2)  # INVALID and BULK002

        # Verify specific errors
        invalid_employee_error = next(r for r in error_records if r["badge_id"] == "INVALID")
        self.assertIn("Employee with badge ID INVALID not found", invalid_employee_error["errors"])

        invalid_leave_error = next(r for r in error_records if r["leave_type"] == "Invalid Leave")
        self.assertIn("Leave type Invalid Leave not found", invalid_leave_error["errors"])


class LeaveBalanceIntegrationTestCase(TestCase):
    """Integration test cases for complete leave balance migration workflow"""

    def setUp(self):
        """Set up comprehensive test data"""
        self.company = Company.objects.create(company="Razorpay Technologies")
        self.department = Department.objects.create(department="Engineering")
        self.department.company_id.set([self.company])
        self.job_position = JobPosition.objects.create(job_position="Software Engineer", department_id=self.department)

        # Create comprehensive leave types
        self.leave_type_configs = {
            "Earned Leave": {"total_days": 21.0, "payment": "paid", "carryforward_type": "carryforward"},
            "Sick Leave": {"total_days": 12.0, "payment": "paid", "require_attachment": "yes"},
            "Casual Leave": {"total_days": 12.0, "payment": "paid"},
            "Maternity Leave": {"total_days": 180.0, "payment": "paid", "require_attachment": "yes"},
            "Paternity Leave": {"total_days": 15.0, "payment": "paid", "require_attachment": "yes"}
        }

        self.leave_types = {}
        for name, config in self.leave_type_configs.items():
            self.leave_types[name] = LeaveType.objects.create(
                name=name,
                company_id=self.company,
                **config
            )

        # Create test employees with realistic data
        self.employees = []
        employee_data = [
            {"badge_id": "RZP001", "name": "John", "surname": "Doe"},
            {"badge_id": "RZP002", "name": "Jane", "surname": "Smith"},
            {"badge_id": "RZP003", "name": "Bob", "surname": "Johnson"},
            {"badge_id": "RZP004", "name": "Alice", "surname": "Brown"},
            {"badge_id": "RZP005", "name": "Charlie", "surname": "Wilson"}
        ]

        for emp_data in employee_data:
            user = User.objects.create_user(
                username=emp_data["badge_id"].lower(),
                email=f"{emp_data['name'].lower()}.{emp_data['surname'].lower()}@razorpay.com"
            )
            employee = Employee.objects.create(
                badge_id=emp_data["badge_id"],
                employee_first_name=emp_data["name"],
                employee_last_name=emp_data["surname"],
                email=f"{emp_data['name'].lower()}.{emp_data['surname'].lower()}@razorpay.com",
                phone=f"9876543{len(self.employees):02d}",
                employee_user_id=user
            )

            EmployeeWorkInformation.objects.create(
                employee_id=employee,
                job_position_id=self.job_position,
                department_id=self.department,
                company_id=self.company,
                date_joining=date.today()
            )

            self.employees.append(employee)

    def test_complete_migration_workflow(self):
        """Test the complete migration workflow from CSV to database"""
        # Simulate CSV data with realistic leave balances
        csv_data = [
            {"Employee ID": "RZP001", "Earned Leave Balance": 18.5, "Earned Leave Total": 21.0,
             "Sick Leave Balance": 10.0, "Sick Leave Total": 12.0, "Casual Leave Balance": 8.5, "Casual Leave Total": 12.0},
            {"Employee ID": "RZP002", "Earned Leave Balance": 20.0, "Earned Leave Total": 21.0,
             "Sick Leave Balance": 12.0, "Sick Leave Total": 12.0, "Casual Leave Balance": 11.0, "Casual Leave Total": 12.0},
            {"Employee ID": "RZP003", "Earned Leave Balance": 15.5, "Earned Leave Total": 21.0,
             "Sick Leave Balance": 8.0, "Sick Leave Total": 12.0, "Casual Leave Balance": 9.5, "Casual Leave Total": 12.0},
        ]

        # Process the migration
        for row in csv_data:
            employee = Employee.objects.get(badge_id=row["Employee ID"])

            # Create leave balances for each leave type
            leave_mappings = [
                ("Earned Leave", row["Earned Leave Balance"], row["Earned Leave Total"]),
                ("Sick Leave", row["Sick Leave Balance"], row["Sick Leave Total"]),
                ("Casual Leave", row["Casual Leave Balance"], row["Casual Leave Total"])
            ]

            for leave_type_name, balance, total in leave_mappings:
                leave_type = self.leave_types[leave_type_name]

                AvailableLeave.objects.create(
                    employee_id=employee,
                    leave_type_id=leave_type,
                    available_days=balance,
                    total_leave_days=total,
                    carryforward_days=0.0
                )

        # Verify migration results
        total_leave_records = AvailableLeave.objects.count()
        self.assertEqual(total_leave_records, 9)  # 3 employees × 3 leave types

        # Verify specific employee data
        emp1 = Employee.objects.get(badge_id="RZP001")
        emp1_earned = AvailableLeave.objects.get(employee_id=emp1, leave_type_id=self.leave_types["Earned Leave"])
        self.assertEqual(emp1_earned.available_days, 18.5)
        self.assertEqual(emp1_earned.total_leave_days, 21.0)

        emp1_sick = AvailableLeave.objects.get(employee_id=emp1, leave_type_id=self.leave_types["Sick Leave"])
        self.assertEqual(emp1_sick.available_days, 10.0)
        self.assertEqual(emp1_sick.total_leave_days, 12.0)

        # Verify all employees have correct leave types assigned
        for employee in self.employees[:3]:  # First 3 employees have data
            employee_leaves = AvailableLeave.objects.filter(employee_id=employee)
            self.assertEqual(employee_leaves.count(), 3)  # Should have 3 leave types

            leave_type_names = set(employee_leaves.values_list('leave_type_id__name', flat=True))
            expected_types = {"Earned Leave", "Sick Leave", "Casual Leave"}
            self.assertEqual(leave_type_names, expected_types)

    def test_error_handling_and_reporting(self):
        """Test error handling during migration process"""
        # Test data with various error scenarios
        problematic_data = [
            {"Employee ID": "INVALID001", "Earned Leave Balance": 18.5, "Earned Leave Total": 21.0},  # Invalid employee
            {"Employee ID": "RZP001", "Invalid Leave Balance": 10.0, "Invalid Leave Total": 12.0},  # Invalid leave type
            {"Employee ID": "RZP002", "Earned Leave Balance": -5.0, "Earned Leave Total": 21.0},  # Negative balance
            {"Employee ID": "RZP003", "Earned Leave Balance": 25.0, "Earned Leave Total": 21.0},  # Balance > Total
        ]

        errors = []
        successful_imports = []

        for row in problematic_data:
            try:
                employee = Employee.objects.get(badge_id=row["Employee ID"])

                # Process each leave type in the row
                for key, value in row.items():
                    if key.endswith(" Balance"):
                        leave_type_name = key.replace(" Balance", "")
                        total_key = f"{leave_type_name} Total"

                        if total_key in row:
                            balance = value
                            total = row[total_key]

                            # Validate data
                            if balance < 0:
                                errors.append(f"Negative balance for {employee.badge_id}: {leave_type_name}")
                                continue

                            if balance > total:
                                errors.append(f"Balance exceeds total for {employee.badge_id}: {leave_type_name}")
                                continue

                            # Try to find leave type
                            try:
                                leave_type = LeaveType.objects.get(name=leave_type_name)
                                successful_imports.append({
                                    "employee": employee,
                                    "leave_type": leave_type,
                                    "balance": balance,
                                    "total": total
                                })
                            except LeaveType.DoesNotExist:
                                errors.append(f"Leave type not found: {leave_type_name}")

            except Employee.DoesNotExist:
                errors.append(f"Employee not found: {row['Employee ID']}")

        # Verify error detection
        self.assertEqual(len(errors), 4)  # Should detect all 4 error scenarios
        self.assertEqual(len(successful_imports), 0)  # No successful imports due to errors

        # Verify specific error messages
        self.assertIn("Employee not found: INVALID001", errors)
        self.assertIn("Leave type not found: Invalid Leave", errors)
        self.assertIn("Negative balance for RZP002: Earned Leave", errors)
        self.assertIn("Balance exceeds total for RZP003: Earned Leave", errors)


class ManagementCommandTestCase(TestCase):
    """Test cases for Django management commands"""

    def setUp(self):
        """Set up test data"""
        self.company = Company.objects.create(company="Test Company")
        self.department = Department.objects.create(department="IT")
        self.department.company_id.set([self.company])

        # Create some existing leave types
        LeaveType.objects.create(
            name="Annual Leave",
            payment="paid",
            total_days=12.0,
            company_id=self.company
        )

    def test_check_missing_leave_types_command(self):
        """Test the check_missing_leave_types management command"""
        from io import StringIO
        from django.core.management import call_command

        # Capture command output
        out = StringIO()

        # Run command without creating missing types
        call_command('check_missing_leave_types', stdout=out)
        output = out.getvalue()

        # Should report missing leave types
        self.assertIn('missing leave types', output.lower())
        self.assertIn('Earned Leave', output)
        self.assertIn('Sick Leave', output)

    def test_check_missing_leave_types_with_creation(self):
        """Test creating missing leave types via management command"""
        from io import StringIO
        from django.core.management import call_command

        # Count existing leave types
        initial_count = LeaveType.objects.count()

        # Capture command output
        out = StringIO()

        # Run command with creation enabled
        call_command(
            'check_missing_leave_types',
            '--create-missing',
            f'--company-id={self.company.id}',
            stdout=out
        )

        # Should have created new leave types
        final_count = LeaveType.objects.count()
        self.assertGreater(final_count, initial_count)

        # Verify specific leave types were created
        self.assertTrue(LeaveType.objects.filter(name="Earned Leave").exists())
        self.assertTrue(LeaveType.objects.filter(name="Sick Leave").exists())

    def test_import_leave_balances_command_validation(self):
        """Test CSV validation in import_leave_balances command"""
        import tempfile
        import os
        from io import StringIO
        from django.core.management import call_command

        # Create test CSV content
        csv_content = """Employee ID,Earned Leave Balance,Earned Leave Total
RZP001,18.5,21.0
INVALID,20.0,21.0"""

        # Create temporary CSV file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            csv_file_path = f.name

        try:
            # Create test employee
            user = User.objects.create_user(username="testuser", email="<EMAIL>")
            Employee.objects.create(
                badge_id="RZP001",
                employee_first_name="Test",
                employee_last_name="User",
                email="<EMAIL>",
                phone="*********0",
                employee_user_id=user
            )

            # Create leave type
            LeaveType.objects.create(
                name="Earned Leave",
                payment="paid",
                total_days=21.0,
                company_id=self.company
            )

            # Capture command output
            out = StringIO()

            # Run dry run validation
            call_command(
                'import_leave_balances',
                csv_file_path,
                '--dry-run',
                stdout=out
            )

            output = out.getvalue()

            # Should report validation results
            self.assertIn('DRY RUN MODE', output)
            self.assertIn('Valid records: 1', output)
            self.assertIn('Error records: 1', output)

        finally:
            # Clean up temporary file
            os.unlink(csv_file_path)
