"""
Test settings for running Django tests with a separate test database.
This helps avoid conflicts with existing database connections.
"""

from horilla.settings import *
import uuid

# Use a unique test database name to avoid conflicts
TEST_DB_NAME = f'test_horilla_leave_tests_{uuid.uuid4().hex[:8]}'

# Use SQLite for testing to avoid PostgreSQL connection issues
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',  # Use in-memory SQLite database for speed
        'TEST': {
            'NAME': ':memory:',
        },
    }
}

# Disable migrations for faster testing
class DisableMigrations:
    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None

# Uncomment the line below to disable migrations for faster testing
# MIGRATION_MODULES = DisableMigrations()

# Use in-memory cache for testing
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
}

# Speed up password hashing for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Disable debug mode for tests
DEBUG = False

# Use a simple email backend for testing
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Disable external API calls during testing
EXTERNAL_API_CALLS_ENABLED = False

print(f"Test settings loaded. Test database name: {TEST_DB_NAME}")
