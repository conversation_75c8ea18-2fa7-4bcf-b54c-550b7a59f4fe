"""
forms.py

This module contains the form classes used in the application.

Each form represents a specific functionality or data input in the
application. They are responsible for validating
and processing user input data.

Classes:
- YourForm: Represents a form for handling specific data input.

Usage:
from django import forms

class YourForm(forms.Form):
    field_name = forms.CharField()

    def clean_field_name(self):
        # Custom validation logic goes here
        pass
"""

from typing import Any

from django import forms
from django.template.loader import render_to_string
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

from base.forms import ModelForm
from base.methods import filtersubordinatesemployeemodel, is_reportingmanager
from base.models import Department, JobPosition
from employee.forms import MultipleFileField
from employee.models import Employee
from helpdesk.models import (
    FAQ,
    Attachment,
    Comment,
    DepartmentManager,
    FAQCategory,
    Ticket,
    TicketType,
)
from horilla import horilla_middlewares


def validate_file_size(file):
    """
    Validate that the uploaded file size does not exceed 10MB
    """
    max_size = 10 * 1024 * 1024  # 10MB in bytes
    if file.size > max_size:
        raise ValidationError(_("File size exceeds the 10MB limit. Please upload a smaller file."))
    return file


def validate_file_type(file):
    """
    Validate that the uploaded file type is allowed.
    Rejects APK, audio, video, and MP3 files.
    """
    # Get file extension
    filename = file.name.lower()

    # Define blocked extensions
    blocked_extensions = [
        # APK files
        '.apk',
        # Audio files
        '.mp3', '.wav', '.ogg', '.aac', '.flac', '.wma', '.m4a', '.aiff', '.au',
        # Video files
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp',
        '.mpg', '.mpeg', '.ogv', '.ts', '.vob', '.rm', '.rmvb', '.asf', '.divx'
    ]

    # Check if file has a blocked extension
    for ext in blocked_extensions:
        if filename.endswith(ext):
            raise ValidationError(
                _("File type '{}' is not allowed. APK, audio, and video files are not permitted.").format(ext.upper())
            )

    return file


def validate_helpdesk_file(file):
    """
    Combined validation for helpdesk file uploads.
    Validates both file size and file type.
    """
    validate_file_size(file)
    validate_file_type(file)
    return file


class TicketTypeForm(ModelForm):

    class Meta:
        model = TicketType
        fields = "__all__"
        exclude = ["is_active"]

    def clean_name(self):
        """
        Validate that the ticket type name is unique (case-insensitive)
        """
        name = self.cleaned_data.get("name")

        # Get the current instance ID (or None if this is a new ticket type)
        instance_id = getattr(self.instance, 'id', None)

        # Check if a ticket type with this name already exists
        if name and TicketType.objects.filter(name__iexact=name).exclude(id=instance_id).exists():
            # Add a class to make the error more visible
            raise forms.ValidationError("A ticket type with this name already exists.")

        return name

    def as_p(self, *args, **kwargs):
        """
        Render the form fields as HTML table rows with Bootstrap styling.
        """
        context = {"form": self}
        table_html = render_to_string("horilla_form.html", context)
        return table_html

    def is_valid(self):
        """
        Override is_valid to ensure errors are properly processed
        """
        valid = super().is_valid()
        # If not valid and there's no response from the frontend,
        # we'll make sure the errors get displayed
        if not valid:
            for field, errors in self.errors.items():
                for error in errors:
                    # This will ensure the error gets logged at minimum
                    print(f"Validation error in {field}: {error}")
        return valid


class FAQForm(ModelForm):
    class Meta:
        model = FAQ
        fields = "__all__"
        exclude = ["is_active"]
        widgets = {
            "category": forms.HiddenInput(),
            "tags": forms.SelectMultiple(
                attrs={
                    "class": "oh-select oh-select-2 select2-hidden-accessible",
                    "onchange": "updateTag(this)",
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        """
        Initializes the Ticket tag form instance.
        If an instance is provided, sets the initial value for the form's .
        """
        super().__init__(*args, **kwargs)
        self.fields["tags"].choices = list(self.fields["tags"].choices)
        self.fields["tags"].choices.append(("create_new_tag", "Create new tag"))


class TicketForm(ModelForm):
    # deadline = forms.DateField(widget=forms.DateInput(attrs={"type": "date"}))

    class Meta:
        model = Ticket
        fields = [
            "id",
            "title",
            "employee_id",
            "description",
            "ticket_type",
            "priority",
            # "assigning_type",
            # "raised_on",
            # "deadline",
            "status",
            # "tags",
            "company_id",
        ]
        widgets = {
            "raised_on": forms.Select(
                attrs={"class": "oh-select oh-select-2", "required": "true"}
            ),
            "employee_id": forms.Select(
                attrs={
                    "class": "oh-select oh-select-2",
                    "data-placeholder": "Anonymous"
                }
            ),
            "company_id": forms.Select(
                attrs={"class": "oh-select oh-select-2 select2-hidden-accessible"}
            ),
        }

    def as_p(self, *args, **kwargs):
        """
        Render the form fields as HTML table rows with Bootstrap styling.
        """
        context = {"form": self}
        table_html = render_to_string("horilla_form.html", context)
        return table_html

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            self.fields.pop("attachment", None)
        else:
            self.fields["attachment"] = MultipleFileField(
                label="Attachments",
                required=False,
                help_text=_("Maximum file size: 10MB per file. APK, audio, and video files are not allowed.")
            )
            self.fields["attachment"].widget.attrs = {
                "class": "form-control",
                "style": "width: 100% !important; box-sizing: border-box;",
                "multiple": True,
                "accept": ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.svg,.zip,.rar",
            }
        request = getattr(horilla_middlewares._thread_locals, "request", None)
        # instance = kwargs.get("instance")
        # if instance:
        #     employee = instance.employee_id
        # else:
        #     employee = request.user.employee_get

        # initialising employee queryset according to the user
        # self.fields["employee_id"].queryset = filtersubordinatesemployeemodel(
        #     request, Employee.objects.filter(is_active=True), perm="helpdesk.add_ticket"
        # ) | Employee.objects.filter(employee_user_id=request.user)
        # self.fields["employee_id"].initial = employee
        self.fields["employee_id"].required = False
        self.fields["employee_id"].empty_label = "Anonymous"
        self.fields["employee_id"].initial = None
        self.fields["employee_id"].choices = [("", "Anonymous")] + list(self.fields["employee_id"].choices)

        # Show company_id field only for superusers
        if request and request.user.is_superuser:
            # Make sure company_id is visible and required for superusers
            self.fields["company_id"].required = True
            from base.models import Company
            self.fields["company_id"].queryset = Company.objects.all()
            # Set default to HQ company if available
            hq_company = Company.objects.filter(hq=True).first()
            if hq_company:
                self.fields["company_id"].initial = hq_company
        else:
            # Hide company_id field for non-superusers
            if "company_id" in self.fields:
                self.fields.pop("company_id")

        # appending dynamic create option according to user
        if is_reportingmanager(request) or request.user.has_perm(
            "helpdesk.add_tickettype"
        ):
            self.fields["ticket_type"].choices = list(
                self.fields["ticket_type"].choices
            )
            self.fields["ticket_type"].choices.append(
                ("create_new_ticket_type", "Create new ticket type")
            )
        # Commenting out tags section as it's removed from fields
        # if is_reportingmanager(request) or request.user.has_perm("base.add_tags"):
        #     self.fields["tags"].choices = list(self.fields["tags"].choices)
        #     self.fields["tags"].choices.append(("create_new_tag", "Create new tag"))

    def clean_attachment(self):
        """
        Validate file size and file type for each uploaded attachment
        """
        attachments = self.cleaned_data.get("attachment")
        if attachments:
            # Handle both single file and multiple files
            if not isinstance(attachments, list):
                attachments = [attachments]

            for attachment in attachments:
                if attachment:
                    validate_helpdesk_file(attachment)

        return self.cleaned_data.get("attachment")



class TicketTagForm(ModelForm):
    class Meta:
        model = Ticket
        fields = [
            "tags",
        ]
        widgets = {
            "tags": forms.SelectMultiple(
                attrs={
                    "class": "oh-select oh-select-2 select2-hidden-accessible",
                    "onchange": "updateTag()",
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        """
        Initializes the Ticket tag form instance.
        If an instance is provided, sets the initial value for the form's .
        """
        super().__init__(*args, **kwargs)
        request = getattr(horilla_middlewares._thread_locals, "request", None)
        if is_reportingmanager(request) or request.user.has_perm("base.add_tags"):
            self.fields["tags"].choices = list(self.fields["tags"].choices)
            self.fields["tags"].choices.append(("create_new_tag", "Create new tag"))


class TicketRaisedOnForm(ModelForm):
    class Meta:
        model = Ticket
        fields = ["assigning_type", "raised_on"]
        widgets = {
            "raised_on": forms.Select(
                attrs={"class": "oh-select oh-select-2", "required": "true"},
            ),
        }


class TicketAssigneesForm(ModelForm):
    class Meta:
        model = Ticket
        fields = [
            "assigned_to",
        ]


class FAQCategoryForm(ModelForm):
    class Meta:
        model = FAQCategory
        fields = "__all__"
        exclude = ["is_active"]


class CommentForm(forms.ModelForm):
    class Meta:
        model = Comment
        fields = [
            "comment",
        ]
        exclude = ["is_active"]
        widgets = {"employee_id": forms.HiddenInput()}


class AttachmentForm(forms.ModelForm):
    file = forms.FileField(
        widget=forms.TextInput(
            attrs={
                "name": "file",
                "type": "File",
                "class": "form-control",
                "multiple": "True",
                "accept": ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.svg,.zip,.rar",
            }
        ),
        label="",
        help_text=_("Maximum file size: 10MB per file. APK, audio, and video files are not allowed.")
    )

    class Meta:
        model = Attachment
        fields = ["file", "comment", "ticket"]
        exclude = ["is_active"]

    def clean_file(self):
        """
        Validate file size and file type for uploaded attachment
        """
        file = self.cleaned_data.get("file")
        if file:
            validate_helpdesk_file(file)
        return file


class DepartmentManagerCreateForm(ModelForm):
    class Meta:
        model = DepartmentManager
        fields = ["department", "manager"]
        widgets = {
            "department": forms.Select(
                attrs={
                    "onchange": "getDepartmentEmployees($(this))",
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if "instance" in kwargs:
            department = kwargs["instance"].department
            # Get the employees related to this department
            employees = department.employeeworkinformation_set.values_list(
                "employee_id", flat=True
            )
            # Set the manager field queryset to be those employees
            self.fields["manager"].queryset = Employee.objects.filter(id__in=employees)
