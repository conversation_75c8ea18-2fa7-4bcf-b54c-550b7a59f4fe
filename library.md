# Context7 Library IDs

This file maintains a list of Context7-compatible library IDs that have been used in the Horilla HRMS project.

**Project**: Horilla - Open Source HRMS (Human Resource Management System)
**Framework**: Django 4.2.11
**Language**: Python 3.10+
**Frontend**: Bootstrap, jQuery, Alpine.js

## Core Framework

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| Django | /django/django | Web framework for Python |
| Django REST Framework | /encode/django-rest-framework | Powerful and flexible toolkit for building Web APIs |

## Django Extensions & Plugins

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| django-cors-headers | /adamchainz/django-cors-headers | Django app for handling CORS headers |
| django-environ | /joke2k/django-environ | Django environment variables management |
| django-filter | /carltongibson/django-filter | Django filtering for querysets |
| django-import-export | /django-import-export/django-import-export | Django library for importing and exporting data |
| django-simple-history | /jazzband/django-simple-history | Store model history and view/revert changes |
| django-widget-tweaks | /jazzband/django-widget-tweaks | Tweak form field rendering in templates |
| django-apscheduler | /jcass77/django-apscheduler | Django APScheduler integration |
| django-select2 | /applegrew/django-select2 | Django Select2 integration |
| django-storages | /jschneier/django-storages | Collection of custom storage backends |
| django-redis | /jazzband/django-redis | Redis cache backend for Django |
| django-cryptography | /georgemarshall/django-cryptography | Cryptographic fields for Django |
| django-auditlog | /jjkester/django-auditlog | Track changes to Django models |
| django-mathfilters | /dbrgn/django-mathfilters | Math filters for Django templates |
| django-microsoft-auth | /AngellusMortis/django_microsoft_auth | Microsoft authentication for Django |
| django-model-utils | /jazzband/django-model-utils | Django model utilities |
| django-jsonfield | /adamchainz/django-jsonfield | JSONField for Django models |

## Authentication & Security

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| djangorestframework-simplejwt | /jazzband/djangorestframework-simplejwt | JSON Web Token authentication for DRF |
| cryptography | /pyca/cryptography | Cryptographic recipes and primitives |
| oauthlib | /oauthlib/oauthlib | Generic OAuth implementation |

## API Documentation

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| drf-yasg | /axnsan12/drf-yasg | Swagger/OpenAPI documentation for Django REST Framework |

## Task Scheduling

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| APScheduler | /agronholm/apscheduler | Advanced Python Scheduler |

## Data Processing & Export

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| pandas | /pandas-dev/pandas | Data analysis and manipulation library |
| numpy | /numpy/numpy | Fundamental package for scientific computing |
| openpyxl | /openpyxl/openpyxl | Read/write Excel 2010 xlsx/xlsm/xltx/xltm files |
| XlsxWriter | /jmcnamara/XlsxWriter | Python module for creating Excel XLSX files |
| reportlab | /reportlab/reportlab | PDF generation library |

## PDF & Document Processing

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| PyMuPDF | /pymupdf/PyMuPDF | Python bindings for MuPDF PDF library |
| pypdf | /py-pdf/pypdf | Pure-python PDF library |
| xhtml2pdf | /xhtml2pdf/xhtml2pdf | HTML/CSS to PDF converter |
| pdfkit | /JazzCore/python-pdfkit | Python wrapper for wkhtmltopdf |
| pyHanko | /MatthiasValvekens/pyHanko | Sign and timestamp PDF documents |

## Image & Graphics Processing

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| Pillow | /python-pillow/Pillow | Python Imaging Library |
| qrcode | /lincolnloop/python-qrcode | QR code generator |
| svglib | /deeplook/svglib | SVG to PDF/PS converter |

## Web Scraping & HTML Processing

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| beautifulsoup4 | /wention/BeautifulSoup4 | HTML/XML parser |
| lxml | /lxml/lxml | XML and HTML processing library |
| html5lib | /html5lib/html5lib-python | HTML parser based on WHATWG HTML specification |

## HTTP & API Clients

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| requests | /psf/requests | HTTP library for Python |
| httpx | /encode/httpx | Next generation HTTP client |
| urllib3 | /urllib3/urllib3 | HTTP client library |

## Cloud Services

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| google-api-python-client | /googleapis/google-api-python-client | Google API client library |
| google-cloud-storage | /googleapis/python-storage | Google Cloud Storage client |
| boto3 | /boto/boto3 | AWS SDK for Python |

## Database & Caching

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| psycopg2-binary | /psycopg/psycopg2 | PostgreSQL adapter for Python |
| redis | /redis/redis-py | Redis Python client |

## Search

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| django-haystack | /django-haystack/django-haystack | Modular search for Django |
| Whoosh | /whoosh-community/whoosh | Fast, pure-Python full text indexing |

## Monitoring & Error Tracking

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| sentry-sdk | /getsentry/sentry-python | Sentry SDK for Python |

## Deployment & Production

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| gunicorn | /benoitc/gunicorn | Python WSGI HTTP Server |
| whitenoise | /evansd/whitenoise | Static file serving for Python web apps |

## Testing Libraries

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| pytest | /pytest-dev/pytest | Python testing framework |
| pytest-django | /pytest-dev/pytest-django | Django plugin for pytest |
| pytest-cov | /pytest-dev/pytest-cov | Coverage plugin for pytest |
| responses | /getsentry/responses | Mock HTTP requests library |

## Biometric & Hardware Integration

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| pyzk | /fananimi/pyzk | ZKTeco biometric device integration |

## Utilities & Helpers

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| python-dateutil | /dateutil/dateutil | Extensions to the standard Python datetime module |
| pytz | /stub42/pytz | World timezone definitions |
| python-bidi | /MeirKriheli/python-bidi | BiDi layout implementation |
| arabic-reshaper | /mpcabd/python-arabic-reshaper | Arabic text reshaping |
| python-dotenv | /theskumar/python-dotenv | Load environment variables from .env file |
| click | /pallets/click | Command line interface creation kit |
| PyYAML | /yaml/pyyaml | YAML parser and emitter |

## Frontend Libraries (JavaScript/CSS)

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| jQuery | /jquery/jquery | JavaScript library |
| jQuery UI | /jquery/jquery-ui | User interface interactions and widgets |
| Alpine.js | /alpinejs/alpine | Lightweight JavaScript framework |
| Bootstrap | /twbs/bootstrap | CSS framework |
| Select2 | /select2/select2 | jQuery replacement for select boxes |
| Ionicons | /ionic-team/ionicons | Icon library |

## Development Tools

| Library Name | Context7 Library ID | Description |
|--------------|---------------------|-------------|
| laravel-mix | /laravel-mix/laravel-mix | Asset compilation wrapper |

## Usage Notes

- This project uses Django 4.2.11 as the core framework
- PostgreSQL is the recommended database backend
- Redis is used for caching and session storage
- The project includes comprehensive HRMS modules: recruitment, employee management, attendance, leave, payroll, PMS, assets, and more
- Testing is configured with pytest and includes coverage reporting
- The frontend uses Bootstrap with jQuery and Alpine.js for interactivity
- PDF generation is handled through multiple libraries depending on use case
- Biometric device integration is available through pyzk
- Cloud storage support for AWS S3 and Google Cloud Storage
- Comprehensive audit logging and history tracking
- Multi-language support with internationalization
